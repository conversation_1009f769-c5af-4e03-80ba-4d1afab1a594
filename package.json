{"name": "odude-chat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@mantine/core": "^8.2.2", "@mantine/form": "^8.2.2", "@mantine/hooks": "^8.2.2", "@mantine/modals": "^8.2.2", "@mantine/notifications": "^8.2.2", "@supabase/supabase-js": "^2.53.0", "@tabler/icons-react": "^3.34.1", "@xenova/transformers": "^2.17.2", "jose": "^6.0.12", "next": "15.4.5", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "server-only": "^0.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}