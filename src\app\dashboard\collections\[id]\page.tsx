import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { notFound } from 'next/navigation';
import { QAPairsView } from '@/components/qa-pairs/qa-pairs-view';

interface CollectionPageProps {
  params: {
    id: string;
  };
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  const user = await getCurrentUser();
  
  // Fetch the collection and verify ownership
  const { data: collection, error: collectionError } = await supabase
    .from('collections')
    .select('*')
    .eq('id', params.id)
    .eq('user_id', user.id)
    .single();

  if (collectionError || !collection) {
    notFound();
  }

  // Fetch Q&A pairs for this collection
  const { data: qaPairs, error: qaPairsError } = await supabase
    .from('qa_pairs')
    .select('*')
    .eq('collection_id', params.id)
    .order('created_at', { ascending: false });

  if (qaPairsError) {
    console.error('Error fetching Q&A pairs:', qaPairsError);
  }

  return (
    <QAPairsView 
      collection={collection}
      qaPairs={qaPairs || []}
    />
  );
}
