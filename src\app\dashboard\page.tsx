import { getCurrentUser } from '@/lib/auth';
import { 
  Title, 
  Text, 
  Card, 
  Group, 
  Stack, 
  Button,
  SimpleGrid,
  ThemeIcon,
} from '@mantine/core';
import { 
  IconCollection, 
  IconQuestionMark, 
  IconPlus 
} from '@tabler/icons-react';
import Link from 'next/link';

export default async function DashboardPage() {
  const user = await getCurrentUser();

  return (
    <Stack gap="lg">
      <div>
        <Title order={1}>Welcome back, {user.name}!</Title>
        <Text c="dimmed" size="lg">
          Manage your Q&A collections and knowledge base
        </Text>
      </div>

      <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="lg">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Collections</Text>
            <ThemeIcon color="blue" variant="light">
              <IconCollection size="1.1rem" />
            </ThemeIcon>
          </Group>

          <Text size="sm" c="dimmed" mb="md">
            Organize your questions and answers into collections for easy management.
          </Text>

          <Button 
            component={Link} 
            href="/dashboard/collections" 
            variant="light" 
            fullWidth
          >
            View Collections
          </Button>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Quick Actions</Text>
            <ThemeIcon color="green" variant="light">
              <IconPlus size="1.1rem" />
            </ThemeIcon>
          </Group>

          <Text size="sm" c="dimmed" mb="md">
            Create a new collection to start organizing your Q&A pairs.
          </Text>

          <Button 
            component={Link} 
            href="/dashboard/collections?action=create" 
            variant="light" 
            color="green"
            fullWidth
          >
            Create Collection
          </Button>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>Getting Started</Text>
            <ThemeIcon color="orange" variant="light">
              <IconQuestionMark size="1.1rem" />
            </ThemeIcon>
          </Group>

          <Text size="sm" c="dimmed" mb="md">
            Learn how to make the most of your Q&A management system.
          </Text>

          <Button variant="light" color="orange" fullWidth disabled>
            View Guide (Coming Soon)
          </Button>
        </Card>
      </SimpleGrid>
    </Stack>
  );
}
